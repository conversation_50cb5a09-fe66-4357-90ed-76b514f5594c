import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { eq, desc, asc, like, count, sql, and, or, gte } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { users, cats, messages, favorites, catImages } from "@/lib/db/schema";
import { logSlowQuery } from "./helpers/cat-helpers";

// Admin authorization middleware
const adminProcedure = protectedProcedure.use(async ({ ctx, next }) => {
	const startTime = performance.now();
	const userId = Number(ctx.user.id);

	// Get user with role from database
	const user = await ctx.db.query.users.findFirst({
		where: eq(users.id, userId),
		columns: {
			id: true,
			role: true,
		},
	});

	const duration = performance.now() - startTime;
	logSlowQuery("adminAuthCheck", duration);

	if (!user || user.role !== "admin") {
		throw new TRPCError({
			code: "FORBIDDEN",
			message: "Admin access required",
		});
	}

	return next({
		ctx: {
			...ctx,
			user: {
				...ctx.user,
				role: user.role,
			},
		},
	});
});

// Input schemas
const listUsersSchema = z.object({
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
	search: z.string().optional(),
	role: z.enum(["admin", "adopter", "rescuer", "clinic"]).optional(),
	sortBy: z.enum(["name", "email", "createdAt", "role"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const listCatsSchema = z.object({
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
	search: z.string().optional(),
	status: z
		.enum(["available", "pending", "adopted", "unavailable"])
		.optional(),
	gender: z.enum(["male", "female"]).optional(),
	breedId: z.string().optional(),
	sortBy: z.enum(["name", "createdAt", "updatedAt"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

const updateUserRoleSchema = z.object({
	userId: z.number(),
	role: z.enum(["admin", "adopter", "rescuer", "clinic"]),
});

const updateCatStatusSchema = z.object({
	catId: z.number(),
	status: z.enum(["available", "pending", "adopted", "unavailable"]),
});

const deleteUserSchema = z.object({
	userId: z.number(),
});

const deleteCatSchema = z.object({
	catId: z.number(),
});

export const adminRouter = createTRPCRouter({
	// Dashboard statistics
	getStats: adminProcedure.query(async ({ ctx }) => {
		const startTime = performance.now();

		const [
			totalUsers,
			totalCats,
			totalMessages,
			totalFavorites,
			usersByRole,
			catsByStatus,
			recentUsers,
			recentCats,
		] = await Promise.all([
			// Total counts
			ctx.db.select({ count: count() }).from(users),
			ctx.db.select({ count: count() }).from(cats),
			ctx.db.select({ count: count() }).from(messages),
			ctx.db.select({ count: count() }).from(favorites),

			// Users by role
			ctx.db
				.select({
					role: users.role,
					count: count(),
				})
				.from(users)
				.groupBy(users.role),

			// Cats by status
			ctx.db
				.select({
					status: cats.status,
					count: count(),
				})
				.from(cats)
				.groupBy(cats.status),

			// Recent users (last 7 days)
			ctx.db
				.select({ count: count() })
				.from(users)
				.where(gte(users.createdAt, sql`NOW() - INTERVAL '7 days'`)),

			// Recent cats (last 7 days)
			ctx.db
				.select({ count: count() })
				.from(cats)
				.where(gte(cats.createdAt, sql`NOW() - INTERVAL '7 days'`)),
		]);

		const duration = performance.now() - startTime;
		logSlowQuery("adminGetStats", duration);

		return {
			totalUsers: totalUsers[0]?.count || 0,
			totalCats: totalCats[0]?.count || 0,
			totalMessages: totalMessages[0]?.count || 0,
			totalFavorites: totalFavorites[0]?.count || 0,
			usersByRole: usersByRole.reduce(
				(acc, item) => {
					acc[item.role] = item.count;
					return acc;
				},
				{} as Record<string, number>
			),
			catsByStatus: catsByStatus.reduce(
				(acc, item) => {
					acc[item.status] = item.count;
					return acc;
				},
				{} as Record<string, number>
			),
			recentUsers: recentUsers[0]?.count || 0,
			recentCats: recentCats[0]?.count || 0,
		};
	}),

	// User management
	listUsers: adminProcedure
		.input(listUsersSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { limit, offset, search, role, sortBy, sortOrder } = input;

			let whereConditions = [];

			if (search) {
				whereConditions.push(
					or(
						like(users.name, `%${search}%`),
						like(users.email, `%${search}%`)
					)
				);
			}

			if (role) {
				whereConditions.push(eq(users.role, role));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			const orderBy =
				sortOrder === "asc" ? asc(users[sortBy]) : desc(users[sortBy]);

			const [usersList, totalCount] = await Promise.all([
				ctx.db.query.users.findMany({
					where: whereClause,
					limit,
					offset,
					orderBy,
					with: {
						wilaya: true,
						commune: true,
					},
				}),
				ctx.db
					.select({ count: count() })
					.from(users)
					.where(whereClause),
			]);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListUsers", duration);

			return {
				users: usersList,
				total: totalCount[0]?.count || 0,
				limit,
				offset,
			};
		}),

	updateUserRole: adminProcedure
		.input(updateUserRoleSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { userId, role } = input;

			const updatedUser = await ctx.db
				.update(users)
				.set({
					role,
					updatedAt: new Date(),
				})
				.where(eq(users.id, userId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminUpdateUserRole", duration);

			if (!updatedUser.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			return updatedUser[0];
		}),

	deleteUser: adminProcedure
		.input(deleteUserSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { userId } = input;

			// Check if user exists
			const user = await ctx.db.query.users.findFirst({
				where: eq(users.id, userId),
			});

			if (!user) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "User not found",
				});
			}

			// Don't allow deleting other admins
			if (user.role === "admin") {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "Cannot delete admin users",
				});
			}

			// Delete user (cascade will handle related records)
			await ctx.db.delete(users).where(eq(users.id, userId));

			const duration = performance.now() - startTime;
			logSlowQuery("adminDeleteUser", duration);

			return { success: true };
		}),

	// Cat management
	listCats: adminProcedure
		.input(listCatsSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const {
				limit,
				offset,
				search,
				status,
				gender,
				breedId,
				sortBy,
				sortOrder,
			} = input;

			let whereConditions = [];

			if (search) {
				whereConditions.push(like(cats.name, `%${search}%`));
			}

			if (status) {
				whereConditions.push(eq(cats.status, status));
			}

			if (gender) {
				whereConditions.push(eq(cats.gender, gender));
			}

			if (breedId) {
				whereConditions.push(eq(cats.breedId, parseInt(breedId)));
			}

			const whereClause =
				whereConditions.length > 0
					? and(...whereConditions)
					: undefined;

			const orderBy =
				sortOrder === "asc" ? asc(cats[sortBy]) : desc(cats[sortBy]);

			const [catsList, totalCount] = await Promise.all([
				ctx.db.query.cats.findMany({
					where: whereClause,
					limit,
					offset,
					orderBy,
					with: {
						user: {
							columns: {
								id: true,
								name: true,
								email: true,
								role: true,
								slug: true,
							},
						},
						images: {
							limit: 1,
							orderBy: asc(catImages.createdAt),
						},
						wilaya: true,
						commune: true,
					},
				}),
				ctx.db.select({ count: count() }).from(cats).where(whereClause),
			]);

			const duration = performance.now() - startTime;
			logSlowQuery("adminListCats", duration);

			return {
				cats: catsList,
				total: totalCount[0]?.count || 0,
				limit,
				offset,
			};
		}),

	updateCatStatus: adminProcedure
		.input(updateCatStatusSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { catId, status } = input;

			const updatedCat = await ctx.db
				.update(cats)
				.set({
					status,
					updatedAt: new Date(),
				})
				.where(eq(cats.id, catId))
				.returning();

			const duration = performance.now() - startTime;
			logSlowQuery("adminUpdateCatStatus", duration);

			if (!updatedCat.length) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			return updatedCat[0];
		}),

	deleteCat: adminProcedure
		.input(deleteCatSchema)
		.mutation(async ({ ctx, input }) => {
			const startTime = performance.now();
			const { catId } = input;

			// Check if cat exists
			const cat = await ctx.db.query.cats.findFirst({
				where: eq(cats.id, catId),
			});

			if (!cat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Delete cat (cascade will handle related records)
			await ctx.db.delete(cats).where(eq(cats.id, catId));

			const duration = performance.now() - startTime;
			logSlowQuery("adminDeleteCat", duration);

			return { success: true };
		}),
});
